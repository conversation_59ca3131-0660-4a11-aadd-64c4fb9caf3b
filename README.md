# Neural API Telegram Bot

A Telegram bot that provides access to the Neural API prompt generation system. Users can interact with the AI through Telegram to generate sophisticated prompts.

## Features

- **Command-based interaction**: Use `/new_prompt` to start a new session
- **Conversation management**: Maintains separate conversation states for different users
- **User authorization**: Only authorized users can access the bot
- **Long message handling**: Automatically splits long responses to fit Telegram's limits
- **Error handling**: Graceful error handling with user-friendly messages

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Create a Telegram Bot

1. Open Telegram and search for `@BotFather`
2. Send `/newbot` command
3. Follow the instructions to create your bot
4. Copy the bot token provided by BotFather

### 3. Get Your Telegram User ID

1. Send a message to your bot
2. Visit `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
3. Look for your user ID in the response

### 4. Configure Environment Variables

Update the `.env` file with your credentials:

```env
NEURAL_API_KEY="your_neural_api_key_here"
TELEGRAM_TOKEN="your_telegram_bot_token_here"
BOT_USER_ID="your_telegram_user_id_here"
```

### 5. Run the Bot

```bash
python NeuralAPI.py
```

## Usage

1. Start a conversation with your bot on Telegram
2. Send `/start` to see the welcome message
3. Send `/new_prompt` to begin a new prompt generation session
4. Describe what you want your prompt to do
5. The bot will generate a sophisticated prompt based on your requirements
6. Send `stop` to end the current session

## Commands

- `/start` - Welcome message and instructions
- `/new_prompt` - Start a new prompt generation session
- `stop` - End the current session

## Security

- The bot only responds to the authorized user ID specified in `BOT_USER_ID`
- All API keys are stored securely in environment variables
- Conversation states are stored in memory and reset when the bot restarts

## Error Handling

- API errors are caught and reported to the user
- Invalid requests are handled gracefully
- Users are notified if they don't have an active session

## Technical Details

- Built with `python-telegram-bot` library
- Uses async/await for efficient handling of multiple users
- Maintains conversation context for each user
- Automatically handles Telegram's message length limits
