#!/bin/bash

# Monitoring script for Flask app and Telegram bot
# This script checks the status of both services and provides management commands

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if PM2 is available
check_pm2() {
    if command -v pm2 &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# Check PM2 services
check_pm2_services() {
    log_header "PM2 Services Status"
    
    if check_pm2; then
        pm2 status
        echo
        
        # Check specific services
        if pm2 describe flask-app &>/dev/null; then
            log_info "Flask app is managed by PM2"
        else
            log_warn "Flask app not found in PM2"
        fi
        
        if pm2 describe telegram-bot &>/dev/null; then
            log_info "Telegram bot is managed by PM2"
        else
            log_warn "Telegram bot not found in PM2"
        fi
    else
        log_warn "PM2 not installed or not available"
    fi
}

# Check systemd services
check_systemd_services() {
    log_header "Systemd Services Status"
    
    # Check Flask app service
    if systemctl is-active --quiet flask-app.service 2>/dev/null; then
        log_info "Flask app service is running"
    elif systemctl list-unit-files | grep -q flask-app.service; then
        log_warn "Flask app service exists but not running"
    else
        log_warn "Flask app service not found"
    fi
    
    # Check Telegram bot service
    if systemctl is-active --quiet telegram-bot.service 2>/dev/null; then
        log_info "Telegram bot service is running"
    elif systemctl list-unit-files | grep -q telegram-bot.service; then
        log_warn "Telegram bot service exists but not running"
    else
        log_warn "Telegram bot service not found"
    fi
}

# Check processes
check_processes() {
    log_header "Process Status"
    
    # Check Flask processes
    flask_procs=$(pgrep -f "python.*app\.py\|flask" | wc -l)
    if [ "$flask_procs" -gt 0 ]; then
        log_info "Found $flask_procs Flask process(es)"
    else
        log_warn "No Flask processes found"
    fi
    
    # Check Telegram bot processes
    bot_procs=$(pgrep -f "python.*NeuralAPI\.py" | wc -l)
    if [ "$bot_procs" -gt 0 ]; then
        log_info "Found $bot_procs Telegram bot process(es)"
    else
        log_warn "No Telegram bot processes found"
    fi
}

# Check ports
check_ports() {
    log_header "Port Usage"
    
    # Common Flask ports
    for port in 5000 8000 80 443; do
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            log_info "Port $port is in use"
        fi
    done
}

# Show resource usage
show_resources() {
    log_header "Resource Usage"
    
    echo "Memory Usage:"
    free -h
    echo
    
    echo "Disk Usage:"
    df -h | grep -E "/$|/home"
    echo
    
    echo "CPU Load:"
    uptime
    echo
}

# Show logs
show_logs() {
    local service=$1
    local lines=${2:-50}
    
    log_header "Recent Logs for $service"
    
    case $service in
        "flask-pm2")
            if check_pm2; then
                pm2 logs flask-app --lines $lines
            fi
            ;;
        "bot-pm2")
            if check_pm2; then
                pm2 logs telegram-bot --lines $lines
            fi
            ;;
        "flask-systemd")
            sudo journalctl -u flask-app.service -n $lines --no-pager
            ;;
        "bot-systemd")
            sudo journalctl -u telegram-bot.service -n $lines --no-pager
            ;;
        *)
            log_error "Unknown service: $service"
            ;;
    esac
}

# Restart services
restart_service() {
    local service=$1
    
    log_header "Restarting $service"
    
    case $service in
        "flask-pm2")
            if check_pm2; then
                pm2 restart flask-app
            fi
            ;;
        "bot-pm2")
            if check_pm2; then
                pm2 restart telegram-bot
            fi
            ;;
        "flask-systemd")
            sudo systemctl restart flask-app.service
            ;;
        "bot-systemd")
            sudo systemctl restart telegram-bot.service
            ;;
        "all-pm2")
            if check_pm2; then
                pm2 restart all
            fi
            ;;
        *)
            log_error "Unknown service: $service"
            ;;
    esac
}

# Main menu
show_menu() {
    echo
    log_header "Service Management Menu"
    echo "1) Check all services status"
    echo "2) Show resource usage"
    echo "3) View PM2 logs"
    echo "4) View systemd logs"
    echo "5) Restart PM2 services"
    echo "6) Restart systemd services"
    echo "7) Exit"
    echo
}

# Handle menu choice
handle_choice() {
    local choice=$1
    
    case $choice in
        1)
            check_pm2_services
            echo
            check_systemd_services
            echo
            check_processes
            echo
            check_ports
            ;;
        2)
            show_resources
            ;;
        3)
            echo "Choose service:"
            echo "1) Flask app"
            echo "2) Telegram bot"
            read -p "Enter choice: " log_choice
            case $log_choice in
                1) show_logs "flask-pm2" ;;
                2) show_logs "bot-pm2" ;;
                *) log_error "Invalid choice" ;;
            esac
            ;;
        4)
            echo "Choose service:"
            echo "1) Flask app"
            echo "2) Telegram bot"
            read -p "Enter choice: " log_choice
            case $log_choice in
                1) show_logs "flask-systemd" ;;
                2) show_logs "bot-systemd" ;;
                *) log_error "Invalid choice" ;;
            esac
            ;;
        5)
            echo "Choose service to restart:"
            echo "1) Flask app"
            echo "2) Telegram bot"
            echo "3) All services"
            read -p "Enter choice: " restart_choice
            case $restart_choice in
                1) restart_service "flask-pm2" ;;
                2) restart_service "bot-pm2" ;;
                3) restart_service "all-pm2" ;;
                *) log_error "Invalid choice" ;;
            esac
            ;;
        6)
            echo "Choose service to restart:"
            echo "1) Flask app"
            echo "2) Telegram bot"
            read -p "Enter choice: " restart_choice
            case $restart_choice in
                1) restart_service "flask-systemd" ;;
                2) restart_service "bot-systemd" ;;
                *) log_error "Invalid choice" ;;
            esac
            ;;
        7)
            log_info "Goodbye!"
            exit 0
            ;;
        *)
            log_error "Invalid choice"
            ;;
    esac
}

# Main function
main() {
    if [ "$1" = "--status" ]; then
        check_pm2_services
        echo
        check_systemd_services
        echo
        check_processes
        exit 0
    fi
    
    while true; do
        show_menu
        read -p "Enter your choice: " choice
        handle_choice "$choice"
        echo
        read -p "Press Enter to continue..."
    done
}

# Run main function
main "$@"
