#!/usr/bin/env python3
"""
Setup script for Neural API Telegram Bot
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False

def check_env_file():
    """Check if .env file is properly configured"""
    print("🔍 Checking .env configuration...")
    
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        return False
    
    with open('.env', 'r') as f:
        content = f.read()
    
    missing_vars = []
    if 'YOUR_TELEGRAM_BOT_TOKEN_HERE' in content:
        missing_vars.append('TELEGRAM_TOKEN')
    if 'YOUR_TELEGRAM_USER_ID_HERE' in content:
        missing_vars.append('BOT_USER_ID')
    
    if missing_vars:
        print(f"❌ Please update the following variables in .env file: {', '.join(missing_vars)}")
        return False
    
    print("✅ .env file looks good!")
    return True

def main():
    """Main setup function"""
    print("🚀 Setting up Neural API Telegram Bot...")
    print("=" * 50)
    
    # Install requirements
    if not install_requirements():
        sys.exit(1)
    
    print()
    
    # Check environment configuration
    if not check_env_file():
        print("\n📝 Please follow these steps:")
        print("1. Create a Telegram bot using @BotFather")
        print("2. Get your Telegram user ID")
        print("3. Update the .env file with your credentials")
        print("4. Run this setup script again")
        sys.exit(1)
    
    print()
    print("🎉 Setup completed successfully!")
    print("📱 You can now run the bot with: python NeuralAPI.py")

if __name__ == "__main__":
    main()
