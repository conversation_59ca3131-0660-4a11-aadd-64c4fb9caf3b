import os
import requests
import logging
import re
from dotenv import load_dotenv
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

load_dotenv()
API_KEY = os.getenv('NEURAL_API_KEY')
TELEGRAM_TOKEN = os.getenv('TELEGRAM_TOKEN')
BOT_USER_ID = os.getenv('BOT_USER_ID')

if not API_KEY:
    raise ValueError("Environment variable NEURAL_API_KEY is not set")
if not TELEGRAM_TOKEN:
    raise ValueError("Environment variable TELEGRAM_TOKEN is not set")
if not BOT_USER_ID:
    raise ValueError("Environment variable BOT_USER_ID is not set")

# 🔍 Double-check this endpoint against your API doc:
url = 'https://api.aichronos.tech/v1/chat/completions'  

model_name = 'gpt-4.1'

initial_prompt = """# 🎨 PROMPT GENERATION SYSTEM

You are now an Prompt Generation Specialist with expertise in creating sophisticated, optimized prompts from user requirements. Your role is to transform user needs into highly effective prompts using advanced techniques and patterns.

## SYSTEM CONFIGURATION

1. REQUIREMENT ANALYSIS
Gather and analyse requirements across these dimensions:

A. CORE OBJECTIVES
- Primary goal and purpose
- Expected outcomes
- Success criteria
- Target audience
- Use context
- Performance expectations
- Format requirements
- Quality standards

B. TECHNICAL NEEDS
- Required capabilities
- System functions
- Tool requirements
- Format specifications
- Resource constraints
- Integration needs
- Processing requirements
- Performance metrics

C. SPECIAL CONSIDERATIONS
- Safety requirements
- Ethical guidelines
- Privacy concerns
- Bias mitigation needs
- Error handling requirements
- Performance criteria
- Format transitions
- Cross-validation needs

2. PROMPT DESIGN FRAMEWORK
Construct the prompt using these building blocks:

A. STRUCTURAL ELEMENTS
- Context setup
- Core instructions
- Technical parameters
- Output specifications
- Error handling
- Quality controls
- Safety protocols
- Format guidelines

B. ADVANCED FEATURES
- Reasoning chains
- Dynamic adaptation
- Self-reflection
- Multi-turn handling
- Format management
- Knowledge integration
- Cross-validation chains
- Style maintenance

C. OPTIMIZATION PATTERNS
- Chain-of-Thought
- Tree-of-Thoughts
- Graph-of-Thought
- Causal Reasoning
- Analogical Reasoning
- Zero-Shot/Few-Shot
- Dynamic Context
- Error Prevention

3. IMPLEMENTATION PATTERNS
Apply these advanced patterns based on requirements:

A. TECHNICAL PATTERNS
- System function integration
- Tool selection strategy
- Multi-modal processing
- Format transition handling
- Resource management
- Error recovery
- Quality verification loops
- Format enforcement rules

B. INTERACTION PATTERNS
- User intent recognition
- Goal alignment
- Feedback loops
- Clarity assurance
- Context preservation
- Dynamic response
- Style consistency
- Pattern adaptation

C. QUALITY PATTERNS
- Output verification
- Consistency checking
- Format validation
- Error detection
- Style maintenance
- Performance monitoring
- Cross-validation chains
- Quality verification loops

D. REASONING CHAINS
- Chain-of-Thought Integration
- Tree-of-Thoughts Implementation
- Graph-of-Thought Patterns
- Causal Reasoning Chains
- Analogical Reasoning Paths
- Cross-Domain Synthesis
- Knowledge Integration Paths
- Logic Flow Patterns

## EXECUTION PROTOCOL

1. First, display:
"🎨 PROMPT GENERATION SYSTEM ACTIVE

Please describe what you want your prompt to do. Include:
- Main purpose/goal
- Expected outputs/results
- Special requirements (technical, format, safety, etc.)
- Any specific features needed
- Quality standards expected
- Format requirements
- Performance expectations

I will generate a sophisticated prompt tailored to your needs."

2. After receiving requirements:
   a) Analyse requirements comprehensively
   b) Map technical needs and constraints
   c) Select appropriate patterns and features
   d) Design prompt architecture
   e) Implement optimizations
   f) Verify against requirements
   g) Validate format handling
   h) Test quality assurance

3. Present the generated prompt in this format:

```markdown
# Generated Prompt: [Purpose/Title]

## Context & Background
[Situational context and background setup]

## Core Role & Capabilities
[Main role definition and key capabilities]

## Technical Configuration
[System functions, tools, and technical setup]

## Operational Guidelines
[Working process and methodology]

## Output Specifications
[Expected outputs and format requirements]

## Advanced Features
[Special capabilities and enhancements]

## Error Handling
[Problem management and recovery]

## Quality Controls
[Success criteria and verification]

## Safety Protocols
[Ethical guidelines and safety measures]

## Format Management
[Format handling and transition protocols]

## Integration Guidelines
[System and tool integration specifications]

## Performance Standards
[Performance criteria and optimization guidelines]
```

4. Provide the complete prompt in a code block for easy copying, followed by:
   - Key features explanation
   - Usage guidelines
   - Customization options
   - Performance expectations
   - Format specifications
   - Quality assurance measures
   - Integration requirements

## QUALITY ASSURANCE

Before delivering the generated prompt, verify:

1. REQUIREMENT ALIGNMENT
- All core needs are addressed
- Technical requirements are met
- Special considerations are handled
- Performance criteria are satisfied
- Format specifications are clear
- Quality standards are defined

2. STRUCTURAL QUALITY
- Clear and logical organization
- Comprehensive coverage
- Coherent flow
- Effective communication
- Pattern consistency
- Style maintenance

3. TECHNICAL ROBUSTNESS
- Proper function integration
- Appropriate tool usage
- Efficient resource usage
- Effective error handling
- Format validation
- Cross-validation chains

4. SAFETY & ETHICS
- Ethical guidelines implemented
- Safety measures included
- Privacy protected
- Bias addressed
- Content validation
- Security protocols

5. USABILITY & ADAPTABILITY
- Easy to understand
- Adaptable to context
- Scalable to needs
- Maintainable over time
- Format flexible
- Integration ready

6. PERFORMANCE OPTIMIZATION
- Resource efficiency
- Response time optimization
- Quality verification loops
- Format enforcement rules
- Style consistency
- Technical efficiency

Activate prompt generation system now.

Share: "🎨 PROMPT GENERATION SYSTEM ACTIVE

Please describe what you want your prompt to do. Include:
- Main purpose/goal
- Expected outputs/results
- Special requirements (technical, format, safety, etc.)
- Any specific features needed
- Quality standards expected
- Format requirements
- Performance expectations

I will generate a sophisticated prompt tailored to your needs
"""

# Store conversation states for different users
user_conversations = {}

headers = {
    'Authorization': f'Bearer {API_KEY}',
    'Content-Type': 'application/json'
}

def convert_markdown_to_telegram(text: str) -> str:
    """Convert standard markdown to Telegram-friendly format."""
    # Use a simpler approach that's more reliable

    # Convert headers to bold
    text = re.sub(r'^#{1,6}\s*(.+)$', r'*\1*', text, flags=re.MULTILINE)

    # Convert **bold** to *bold*
    text = re.sub(r'\*\*([^*]+)\*\*', r'*\1*', text)

    # Convert __bold__ to *bold*
    text = re.sub(r'__([^_]+)__', r'*\1*', text)

    # Keep code blocks as they are (Telegram supports ``` blocks)
    # Keep inline code as it is (Telegram supports `code`)

    return text

def should_use_markdown(text: str) -> bool:
    """Check if text contains markdown formatting that should be preserved."""
    markdown_patterns = [
        r'\*\*.*?\*\*',  # Bold
        r'__.*?__',      # Bold
        r'\*.*?\*',      # Italic
        r'_.*?_',        # Italic
        r'```.*?```',    # Code blocks
        r'`.*?`',        # Inline code
        r'^#{1,6}\s',    # Headers
    ]

    for pattern in markdown_patterns:
        if re.search(pattern, text, re.MULTILINE | re.DOTALL):
            return True
    return False

async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send a message when the command /start is issued."""
    await update.message.reply_text(
        "🎨 Welcome to the Prompt Generation Bot!\n\n"
        "Use /new_prompt to start a new prompt generation session.\n"
        "Then just send me your requirements and I'll help you create sophisticated prompts!"
    )

async def new_prompt_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Start a new prompt generation session."""
    user_id = update.effective_user.id

    # Check if user is authorized (optional - remove if you want to allow all users)
    if str(user_id) != BOT_USER_ID:
        await update.message.reply_text("❌ You are not authorized to use this bot.")
        return

    # Initialize new conversation for this user
    user_conversations[user_id] = [{'role': 'user', 'content': initial_prompt}]

    await update.message.reply_text(
        "🎨 PROMPT GENERATION SYSTEM ACTIVE\n\n"
        "Please describe what you want your prompt to do. Include:\n"
        "- Main purpose/goal\n"
        "- Expected outputs/results\n"
        "- Special requirements (technical, format, safety, etc.)\n"
        "- Any specific features needed\n"
        "- Quality standards expected\n"
        "- Format requirements\n"
        "- Performance expectations\n\n"
        "I will generate a sophisticated prompt tailored to your needs."
    )

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle regular messages from users."""
    user_id = update.effective_user.id
    user_message = update.message.text

    # Check if user is authorized
    if str(user_id) != BOT_USER_ID:
        await update.message.reply_text("❌ You are not authorized to use this bot.")
        return

    # Check if user has an active conversation
    if user_id not in user_conversations:
        await update.message.reply_text(
            "❌ No active prompt generation session. Use /new_prompt to start a new session."
        )
        return

    # Handle stop command
    if user_message.lower() == 'stop':
        del user_conversations[user_id]
        await update.message.reply_text("✅ Prompt generation session ended.")
        return

    # Add user message to conversation
    user_conversations[user_id].append({'role': 'user', 'content': user_message})

    # Send typing indicator
    await context.bot.send_chat_action(chat_id=update.effective_chat.id, action="typing")

    # Prepare API request
    payload = {
        'model': model_name,
        'messages': user_conversations[user_id]
    }

    try:
        # Make API request
        response = requests.post(url, headers=headers, json=payload)

        if response.status_code == 200:
            data = response.json()
            content = data['choices'][0]['message']['content']

            # Add assistant's response to conversation
            user_conversations[user_id].append({'role': 'assistant', 'content': content})

            # Send response to user (split if too long for Telegram)
            await send_long_message(update, content)

        else:
            await update.message.reply_text(
                f"❌ API Error: {response.status_code}\n{response.text}"
            )
            # Remove the last user message if response failed
            user_conversations[user_id].pop()

    except Exception as e:
        logger.error(f"Error processing message: {e}")
        await update.message.reply_text(
            "❌ An error occurred while processing your request. Please try again."
        )
        # Remove the last user message if response failed
        if user_conversations[user_id]:
            user_conversations[user_id].pop()

async def send_long_message(update: Update, message: str) -> None:
    """Send long messages by splitting them if they exceed Telegram's limit."""
    max_length = 4096  # Telegram's message length limit

    # Check if message contains markdown formatting
    use_markdown = should_use_markdown(message)

    if use_markdown:
        try:
            # Convert to Telegram-compatible markdown
            formatted_message = convert_markdown_to_telegram(message)

            if len(formatted_message) <= max_length:
                await update.message.reply_text(
                    formatted_message,
                    parse_mode='Markdown'
                )
            else:
                # Split message into chunks, trying to preserve formatting
                chunks = split_message_smart(formatted_message, max_length)
                for chunk in chunks:
                    try:
                        await update.message.reply_text(
                            chunk,
                            parse_mode='Markdown'
                        )
                    except Exception:
                        # If markdown parsing fails, send as plain text
                        await update.message.reply_text(chunk)
        except Exception as e:
            logger.warning(f"Markdown parsing failed: {e}, sending as plain text")
            # Fallback to plain text
            if len(message) <= max_length:
                await update.message.reply_text(message)
            else:
                chunks = [message[i:i+max_length] for i in range(0, len(message), max_length)]
                for chunk in chunks:
                    await update.message.reply_text(chunk)
    else:
        # Send as plain text
        if len(message) <= max_length:
            await update.message.reply_text(message)
        else:
            chunks = [message[i:i+max_length] for i in range(0, len(message), max_length)]
            for chunk in chunks:
                await update.message.reply_text(chunk)

def split_message_smart(message: str, max_length: int) -> list:
    """Smart message splitting that tries to preserve markdown formatting."""
    if len(message) <= max_length:
        return [message]

    chunks = []
    current_chunk = ""

    # Split by paragraphs first
    paragraphs = message.split('\n\n')

    for paragraph in paragraphs:
        if len(current_chunk) + len(paragraph) + 2 <= max_length:
            if current_chunk:
                current_chunk += '\n\n' + paragraph
            else:
                current_chunk = paragraph
        else:
            if current_chunk:
                chunks.append(current_chunk)
                current_chunk = paragraph
            else:
                # Paragraph is too long, split by lines
                lines = paragraph.split('\n')
                for line in lines:
                    if len(current_chunk) + len(line) + 1 <= max_length:
                        if current_chunk:
                            current_chunk += '\n' + line
                        else:
                            current_chunk = line
                    else:
                        if current_chunk:
                            chunks.append(current_chunk)
                        current_chunk = line

    if current_chunk:
        chunks.append(current_chunk)

    return chunks

def main() -> None:
    """Start the bot."""
    # Create the Application
    application = Application.builder().token(TELEGRAM_TOKEN).build()

    # Add handlers
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("new_prompt", new_prompt_command))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))

    # Run the bot
    logger.info("Starting Telegram bot...")
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == '__main__':
    main()
