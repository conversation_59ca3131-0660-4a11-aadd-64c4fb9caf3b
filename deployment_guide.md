# VPS Deployment Guide: Flask App + Telegram Bot

This guide shows how to run your Telegram bot alongside your Flask web app on a VPS without interference.

## 🏗️ Architecture Overview

```
VPS Server
├── Flask Web App (Port 5000/80/443)
├── Telegram Bot (Background Service)
├── Process Manager (PM2/Supervisor)
└── Reverse Proxy (Nginx) [Optional]
```

## 📋 Prerequisites

- VPS with Python 3.7+
- Flask app already running
- SSH access to your VPS
- Basic Linux knowledge

## 🚀 Deployment Options

### Option 1: Using PM2 (Recommended)

PM2 is a production process manager that keeps both services running.

#### 1. Install PM2
```bash
# Install Node.js and npm (if not installed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install -g pm2
```

#### 2. Create PM2 Configuration
Create `ecosystem.config.js` in your project root:

```javascript
module.exports = {
  apps: [
    {
      name: 'flask-app',
      script: 'app.py',  // Your Flask app file
      interpreter: 'python3',
      cwd: '/path/to/your/flask/app',
      env: {
        FLASK_ENV: 'production',
        PORT: 5000
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G'
    },
    {
      name: 'telegram-bot',
      script: 'NeuralAPI.py',
      interpreter: 'python3',
      cwd: '/path/to/your/telegram/bot',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '500M'
    }
  ]
};
```

#### 3. Deploy with PM2
```bash
# Start both services
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME

# Check status
pm2 status
pm2 logs
```

### Option 2: Using Systemd Services

Create separate systemd services for each application.

#### 1. Create Flask Service
```bash
sudo nano /etc/systemd/system/flask-app.service
```

```ini
[Unit]
Description=Flask Web Application
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/your/flask/app
Environment=PATH=/path/to/your/venv/bin
ExecStart=/path/to/your/venv/bin/python app.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 2. Create Telegram Bot Service
```bash
sudo nano /etc/systemd/system/telegram-bot.service
```

```ini
[Unit]
Description=Neural API Telegram Bot
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/your/telegram/bot
Environment=PATH=/path/to/your/venv/bin
ExecStart=/path/to/your/venv/bin/python NeuralAPI.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 3. Enable and Start Services
```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable services to start on boot
sudo systemctl enable flask-app.service
sudo systemctl enable telegram-bot.service

# Start services
sudo systemctl start flask-app.service
sudo systemctl start telegram-bot.service

# Check status
sudo systemctl status flask-app.service
sudo systemctl status telegram-bot.service
```

### Option 3: Using Screen/Tmux (Simple but not production-ready)

#### Using Screen
```bash
# Start Flask app in background
screen -dmS flask-app bash -c 'cd /path/to/flask/app && python app.py'

# Start Telegram bot in background
screen -dmS telegram-bot bash -c 'cd /path/to/telegram/bot && python NeuralAPI.py'

# List running screens
screen -ls

# Attach to a screen
screen -r flask-app
screen -r telegram-bot
```

## 🔧 Configuration Steps

### 1. Prepare Your VPS

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and pip
sudo apt install python3 python3-pip python3-venv -y

# Create virtual environment (recommended)
python3 -m venv /path/to/your/venv
source /path/to/your/venv/bin/activate
```

### 2. Upload Your Bot Code

```bash
# Create directory for bot
mkdir -p /home/<USER>/telegram-bot
cd /home/<USER>/telegram-bot

# Upload your files (using scp, rsync, or git)
scp -r /local/path/to/bot/* user@your-vps:/home/<USER>/telegram-bot/

# Or clone from git
git clone your-repo-url .
```

### 3. Install Dependencies

```bash
# Activate virtual environment
source /path/to/your/venv/bin/activate

# Install bot dependencies
pip install -r requirements.txt
```

### 4. Configure Environment

```bash
# Copy and edit .env file
cp .env.example .env
nano .env

# Set your credentials
NEURAL_API_KEY="your_api_key"
TELEGRAM_TOKEN="your_bot_token"
BOT_USER_ID="your_user_id"
```

## 🔍 Monitoring and Management

### PM2 Commands
```bash
# View status
pm2 status

# View logs
pm2 logs
pm2 logs flask-app
pm2 logs telegram-bot

# Restart services
pm2 restart flask-app
pm2 restart telegram-bot
pm2 restart all

# Stop services
pm2 stop flask-app
pm2 stop telegram-bot

# Delete services
pm2 delete flask-app
pm2 delete telegram-bot
```

### Systemd Commands
```bash
# Check status
sudo systemctl status flask-app.service
sudo systemctl status telegram-bot.service

# View logs
sudo journalctl -u flask-app.service -f
sudo journalctl -u telegram-bot.service -f

# Restart services
sudo systemctl restart flask-app.service
sudo systemctl restart telegram-bot.service
```

## 🛡️ Security Considerations

1. **Firewall Configuration**
```bash
# Allow only necessary ports
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

2. **Environment Variables**
- Never commit `.env` files to version control
- Use proper file permissions: `chmod 600 .env`

3. **User Permissions**
- Run services as non-root user
- Use virtual environments

## 🔄 Auto-Deployment Script

Create `deploy.sh` for easy updates:

```bash
#!/bin/bash
cd /home/<USER>/telegram-bot
git pull origin main
source /path/to/your/venv/bin/activate
pip install -r requirements.txt
pm2 restart telegram-bot
echo "Deployment completed!"
```

## 📊 Resource Usage

- **Flask App**: ~50-200MB RAM
- **Telegram Bot**: ~30-100MB RAM
- **Total**: Usually under 500MB for both

## 🆘 Troubleshooting

### Common Issues

1. **Port Conflicts**: Ensure Flask and bot use different ports
2. **Permission Errors**: Check file permissions and user ownership
3. **Environment Variables**: Verify `.env` file is loaded correctly
4. **Network Issues**: Check firewall and network connectivity

### Log Locations
- PM2 logs: `~/.pm2/logs/`
- Systemd logs: `journalctl -u service-name`
- Application logs: Check your app's log directory

## 🎯 Recommended Setup

For production, I recommend:
1. **PM2** for process management
2. **Nginx** as reverse proxy for Flask
3. **SSL certificate** for HTTPS
4. **Regular backups** of your configuration
5. **Monitoring** with PM2 or external tools
