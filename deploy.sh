#!/bin/bash

# Telegram Bot Deployment Script
# This script helps deploy and manage your Telegram bot on VPS

set -e  # Exit on any error

# Configuration
BOT_DIR="/home/<USER>/telegram-bot"
VENV_PATH="/home/<USER>/venv"
SERVICE_NAME="telegram-bot"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    log_error "Please don't run this script as root"
    exit 1
fi

# Create directories
create_directories() {
    log_info "Creating directories..."
    mkdir -p "$BOT_DIR"
    mkdir -p "$BOT_DIR/logs"
    mkdir -p "$(dirname "$VENV_PATH")"
}

# Setup virtual environment
setup_venv() {
    log_info "Setting up virtual environment..."
    if [ ! -d "$VENV_PATH" ]; then
        python3 -m venv "$VENV_PATH"
        log_info "Virtual environment created at $VENV_PATH"
    else
        log_info "Virtual environment already exists"
    fi
    
    source "$VENV_PATH/bin/activate"
    pip install --upgrade pip
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    source "$VENV_PATH/bin/activate"
    
    if [ -f "$BOT_DIR/requirements.txt" ]; then
        pip install -r "$BOT_DIR/requirements.txt"
        log_info "Dependencies installed successfully"
    else
        log_warn "requirements.txt not found, installing manually..."
        pip install requests python-dotenv python-telegram-bot
    fi
}

# Setup PM2 service
setup_pm2() {
    log_info "Setting up PM2 service..."
    
    # Check if PM2 is installed
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 is not installed. Please install it first:"
        echo "curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -"
        echo "sudo apt-get install -y nodejs"
        echo "sudo npm install -g pm2"
        exit 1
    fi
    
    # Update ecosystem.config.js with correct paths
    cat > "$BOT_DIR/ecosystem.config.js" << EOF
module.exports = {
  apps: [
    {
      name: 'telegram-bot',
      script: 'NeuralAPI.py',
      interpreter: '$VENV_PATH/bin/python',
      cwd: '$BOT_DIR',
      env: {
        NODE_ENV: 'production'
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '500M',
      error_file: './logs/bot-err.log',
      out_file: './logs/bot-out.log',
      log_file: './logs/bot-combined.log',
      time: true
    }
  ]
};
EOF
    
    log_info "PM2 configuration created"
}

# Deploy bot
deploy_bot() {
    log_info "Deploying Telegram bot..."
    
    cd "$BOT_DIR"
    
    # Stop existing PM2 process if running
    pm2 stop telegram-bot 2>/dev/null || true
    pm2 delete telegram-bot 2>/dev/null || true
    
    # Start the bot
    pm2 start ecosystem.config.js
    pm2 save
    
    log_info "Bot deployed successfully!"
}

# Setup systemd service (alternative to PM2)
setup_systemd() {
    log_info "Setting up systemd service..."
    
    sudo tee /etc/systemd/system/telegram-bot.service > /dev/null << EOF
[Unit]
Description=Neural API Telegram Bot
After=network.target

[Service]
Type=simple
User=$(whoami)
WorkingDirectory=$BOT_DIR
Environment=PATH=$VENV_PATH/bin
ExecStart=$VENV_PATH/bin/python NeuralAPI.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable telegram-bot.service
    sudo systemctl start telegram-bot.service
    
    log_info "Systemd service created and started"
}

# Check environment file
check_env() {
    log_info "Checking environment configuration..."
    
    if [ ! -f "$BOT_DIR/.env" ]; then
        log_warn ".env file not found. Creating template..."
        cat > "$BOT_DIR/.env" << EOF
NEURAL_API_KEY="your_neural_api_key_here"
TELEGRAM_TOKEN="your_telegram_bot_token_here"
BOT_USER_ID="your_telegram_user_id_here"
EOF
        log_warn "Please edit $BOT_DIR/.env with your actual credentials"
        return 1
    fi
    
    # Check if placeholders are still present
    if grep -q "your_.*_here" "$BOT_DIR/.env"; then
        log_warn "Please update the placeholder values in $BOT_DIR/.env"
        return 1
    fi
    
    log_info "Environment configuration looks good"
    return 0
}

# Main deployment function
main() {
    log_info "Starting Telegram Bot deployment..."
    
    create_directories
    setup_venv
    
    # Copy files if they don't exist
    if [ ! -f "$BOT_DIR/NeuralAPI.py" ]; then
        log_warn "Bot files not found in $BOT_DIR"
        log_info "Please copy your bot files to $BOT_DIR first"
        exit 1
    fi
    
    install_dependencies
    
    if ! check_env; then
        log_error "Please configure your .env file first"
        exit 1
    fi
    
    # Ask user for deployment method
    echo
    echo "Choose deployment method:"
    echo "1) PM2 (Recommended)"
    echo "2) Systemd"
    read -p "Enter choice (1 or 2): " choice
    
    case $choice in
        1)
            setup_pm2
            deploy_bot
            log_info "Use 'pm2 status' to check bot status"
            log_info "Use 'pm2 logs telegram-bot' to view logs"
            ;;
        2)
            setup_systemd
            log_info "Use 'sudo systemctl status telegram-bot' to check status"
            log_info "Use 'sudo journalctl -u telegram-bot -f' to view logs"
            ;;
        *)
            log_error "Invalid choice"
            exit 1
            ;;
    esac
    
    log_info "Deployment completed successfully!"
    log_info "Your Telegram bot should now be running alongside your Flask app"
}

# Run main function
main "$@"
