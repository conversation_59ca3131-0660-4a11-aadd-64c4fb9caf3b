module.exports = {
  apps: [
    {
      name: 'flask-app',
      script: 'app.py',  // Change this to your Flask app filename
      interpreter: 'python3',
      cwd: '/path/to/your/flask/app',  // Change this to your Flask app directory
      env: {
        FLASK_ENV: 'production',
        FLASK_APP: 'app.py',
        PORT: 5000
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      error_file: './logs/flask-err.log',
      out_file: './logs/flask-out.log',
      log_file: './logs/flask-combined.log',
      time: true
    },
    {
      name: 'telegram-bot',
      script: 'NeuralAPI.py',
      interpreter: 'python3',
      cwd: '/path/to/your/telegram/bot',  // Change this to your bot directory
      env: {
        NODE_ENV: 'production'
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '500M',
      error_file: './logs/bot-err.log',
      out_file: './logs/bot-out.log',
      log_file: './logs/bot-combined.log',
      time: true
    }
  ]
};
