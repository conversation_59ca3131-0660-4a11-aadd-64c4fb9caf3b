#!/usr/bin/env python3
"""
Simple script to get your Telegram user ID
"""

import requests
from dotenv import load_dotenv
import os

load_dotenv()
TELEGRAM_TOKEN = os.getenv('TELEGRAM_TOKEN')

if not TELEGRAM_TOKEN:
    print("❌ TELEGRAM_TOKEN not found in .env file")
    exit(1)

def get_user_id():
    """Get user ID from Telegram bot updates"""
    url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/getUpdates"
    
    try:
        response = requests.get(url)
        data = response.json()
        
        if not data.get('ok'):
            print(f"❌ Error: {data.get('description', 'Unknown error')}")
            return
        
        updates = data.get('result', [])
        
        if not updates:
            print("❌ No messages found!")
            print("📱 Please send any message to your bot first, then run this script again.")
            return
        
        # Get the most recent message
        latest_update = updates[-1]
        user_id = latest_update['message']['from']['id']
        username = latest_update['message']['from'].get('username', 'No username')
        first_name = latest_update['message']['from'].get('first_name', 'No name')
        
        print("✅ Found your user information:")
        print(f"👤 Name: {first_name}")
        print(f"🔗 Username: @{username}" if username != 'No username' else "🔗 Username: Not set")
        print(f"🆔 User ID: {user_id}")
        print()
        print(f"📝 Add this to your .env file:")
        print(f'BOT_USER_ID="{user_id}"')
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🔍 Getting your Telegram user ID...")
    print("=" * 40)
    get_user_id()
